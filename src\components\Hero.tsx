import { motion } from "framer-motion";
import { BsClipboard<PERSON>heck, BsArrowRight } from "react-icons/bs";
import { Link } from "react-scroll";
import Animated<PERSON>ogo from "./AnimatedLogo";
import Logo2Full from "./LOGO Systems.svg?react";

const Hero = () => {
  return (
    <section
      id="hero"
      className="relative flex min-h-screen items-center justify-center overflow-hidden pt-28 lg:pt-20"
    >
      {/* Logo and CTA Button positioned absolutely with precise spacing */}
      <div className="absolute top-0 left-0 right-0 z-[60] h-20 flex items-center justify-center pointer-events-none">
        <div className="container-custom relative w-full h-full">
          {/* Invisible nav bar replica for spacing reference - centered */}
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 pointer-events-none">
            <div className="invisible inline-flex h-12 items-center rounded-md border border-gray-800 py-3 px-6 pointer-events-none">
              <nav className="flex justify-center items-center pointer-events-none">
                {/* Replica of nav links with same spacing */}
                <span className="font-medium inline-block mx-3.5 pointer-events-none">Home</span>
                <span className="font-medium inline-block mx-3.5 pointer-events-none">Services</span>
                <span className="font-medium inline-block mx-3.5 pointer-events-none">Why Us</span>
                <span className="font-medium inline-block mx-3.5 pointer-events-none">Process</span>
                <span className="font-medium inline-block mx-3.5 pointer-events-none">Why Now</span>
                <span className="font-medium inline-block mx-3.5 pointer-events-none">FAQ</span>
                <span className="font-medium inline-block mx-3.5 pointer-events-none">Contact</span>
              </nav>
            </div>
          </div>

          {/* Logo positioned absolutely - left side with precise distance from nav bar */}
          <motion.div
            initial={{ opacity: 0.5, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5 }}
            className="hidden lg:flex items-center absolute z-[70] pointer-events-auto"
            style={{
              right: "calc(50% + 200px + 12rem)", // 50% (center) + nav bar width + desired distance
              top: "0",
              height: "80px" // Match the header height exactly
            }}
          >
            <span className="flex items-center text-2xl font-bold select-none cursor-pointer">
              <AnimatedLogo
                svg={Logo2Full}
                trigger="mount-hover"
                duration={2000}
                delay={1200}
                style={{ height: "2.5rem", width: "auto" }}
                className="mr-2"
                strokeColor="#9E7FF7"
              />
            </span>
          </motion.div>

          {/* CTA Button positioned absolutely - right side with precise distance from nav bar */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5 }}
            className="hidden lg:flex items-center absolute z-[70] pointer-events-auto"
            style={{
              left: "calc(50% + 200px + 12rem)", // 50% (center) + nav bar width + desired distance
              top: "0",
              height: "80px" // Match the header height exactly
            }}
          >
            <a
              href="https://calendly.com/diftra/intro-call"
              target="_blank"
              rel="noopener noreferrer"
              className="btn-outline-gradient group flex items-center gap-2 px-4 py-2 font-medium transition-all duration-300 hover:-translate-y-1"
            >
              <span>Work With Us</span>
              <motion.span
                className="inline-block"
                animate={{ x: [0, 2, 0] }}
                transition={{
                  duration: 1.5,
                  repeat: Number.POSITIVE_INFINITY,
                  repeatDelay: 4,
                }}
              >
                <BsArrowRight className="transition-all duration-300 group-hover:translate-x-1" />
              </motion.span>
            </a>
          </motion.div>
        </div>
      </div>

      {/* Background gradient */}
      <div className="absolute inset-0 bg-gradient-to-b from-gray-900 via-gray-900 to-gray-800"></div>

      <div className="container-custom relative z-10">
        <div className="flex flex-col items-center text-center">
          <motion.h1
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            className="mb-6 text-4xl font-bold lg:text-5xl xl:text-6xl"
          >
            <span className="gradient-text">Content Automation Ecosystems</span> {" "}
            <br className="hidden lg:block" /> for Creative Agencies
          </motion.h1>

          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="mb-8 max-w-2xl text-lg text-gray-300"
          >
            Streamline your workflow, scale content production, and
            increase ROI with our comprehensive automation solutions
          </motion.p>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            className="flex flex-col space-y-4 sm:flex-row sm:space-x-4 sm:space-y-0"
          >
            <a
              href="https://calendly.com/diftra/intro-call"
              target="_blank"
              rel="noopener noreferrer"
              className="btn-primary flex items-center justify-center gap-2"
            >
              <span>Schedule a Free Consultation</span>
              <motion.span
                className="inline-block"
                animate={{ y: [0, -4, 0] }}
                transition={{
                  duration: 1,
                  repeat: Number.POSITIVE_INFINITY,
                  repeatDelay: 3.5,
                }}
              >
                <BsClipboardCheck />
              </motion.span>
            </a>
            <Link
              to="services"
              spy={true}
              smooth={true}
              offset={-80}
              duration={800}
              className="btn-secondary cursor-pointer"
            >
              Discover Our Solutions
            </Link>
          </motion.div>
        </div>

        {/* Stats Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="mt-16 grid grid-cols-1 gap-24 rounded-lg border border-gray-800 bg-gray-800/30 p-8 backdrop-blur-sm sm:grid-cols-2 sm:[&>*:nth-child(3)]:col-span-2 sm:[&>*:nth-child(3)]:justify-self-center lg:grid-cols-3 lg:[&>*:nth-child(3)]:col-span-1 lg:[&>*:nth-child(3)]:justify-self-auto"
        >
          <div className="text-center">
            <div className="text-3xl font-bold text-purple-400">93%</div>
            <div className="mt-1 text-gray-400">
              Time Saved on Content Creation
            </div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-purple-400">4x</div>
            <div className="mt-1 text-gray-400">
              Content Production Capacity
            </div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-purple-400">16%</div>
            <div className="mt-1 text-gray-400">Higher Profit Margins</div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default Hero;
