import { animate, svg, utils } from "animejs";
import type React from "react";
import { useCallback, useEffect, useRef, memo } from "react";

// Define the types for props
export type AnimatedLogoTrigger = "mount" | "hover" | "scroll" | "both" | "mount-hover";
interface AnimatedLogoProps {
  svg: React.FunctionComponent<React.SVGProps<SVGSVGElement>>;
  trigger?: AnimatedLogoTrigger;
  duration?: number;
  delay?: number;
  className?: string;
  style?: React.CSSProperties;
  strokeColor?: string;
}

// The component is memoized to prevent unnecessary re-renders
const AnimatedLogo: React.FC<AnimatedLogoProps> = ({
  svg: SVGComponent,
  trigger = "mount",
  duration = 2000,
  delay = 0,
  className,
  style,
  strokeColor,
}) => {
  // Refs for DOM elements and state management
  const containerRef = useRef<HTMLDivElement>(null);
  const observerRef = useRef<IntersectionObserver | null>(null);
  // Internal ref to track animation state, preventing concurrent animations
  const animationRunning = useRef(false);
  // Ref to track the last animation start time for safety checks
  const lastAnimationStart = useRef<number>(0);

  // Helper function to set paths to their final drawn state
  const initializePaths = useCallback(() => {
    try {
      if (!containerRef.current) return;
      const svgEl = containerRef.current.querySelector("svg");
      if (!svgEl) return;
      const nodeList = svgEl.querySelectorAll("path, line, polyline");
      if (!nodeList.length) return;

      // Apply stroke color if provided
      if (strokeColor) {
        for (const el of nodeList) {
          (el as SVGElement).setAttribute("stroke", strokeColor);
        }
      }

      // Set all paths to their fully drawn state instantly
      nodeList.forEach((el) => {
        const d = svg.createDrawable(el);
        animate(d, {
          draw: "1 1", // 100% drawn, no gap
          duration: 0,
          loop: false,
        });
      });
    } catch (e) {
      console.error("Failed to initialize logo paths:", e);
    }
  }, [strokeColor]);

  // Animation state management callbacks
  const animatePathsStarted = useCallback(() => {
    animationRunning.current = true;
    lastAnimationStart.current = Date.now();
  }, []);

  const animatePathsCompleted = useCallback(() => {
    animationRunning.current = false;
  }, []);

  // Core animation logic
  const animatePaths = useCallback((useDelay: boolean = true) => {
    // Prevent starting a new animation if one is already running
    if (animationRunning.current) return;

    // Prevent starting animation if tab is not visible
    if (typeof document !== 'undefined' && document.visibilityState === "hidden") return;

    try {
      if (!containerRef.current) return;
      const svgEl = containerRef.current.querySelector("svg");
      if (!svgEl) return;
      const nodeList = svgEl.querySelectorAll("path, line, polyline");
      if (!nodeList.length) return;

      // Update the animation state and timestamp
      animatePathsStarted();

      // Apply stroke color if provided
      if (strokeColor) {
        for (const el of nodeList) {
          (el as SVGElement).setAttribute("stroke", strokeColor);
        }
      }

      // Animation sequence timings
      const drawDuration = duration * 0.3;
      const pauseDuration = duration * 0.1;
      const undrawDuration = duration * 0.3;
      const staggerDelay = 100;

      // Use delay only if useDelay is true (for mount animations)
      const actualDelay = useDelay ? delay : 0;

      const totalDrawTime = drawDuration + (nodeList.length - 1) * staggerDelay;
      const lastUndrawStartTime =
        actualDelay + totalDrawTime + pauseDuration + (nodeList.length - 1) * staggerDelay;
      const totalAnimationDuration = lastUndrawStartTime + undrawDuration;

      // Animate each path
      for (let idx = 0; idx < nodeList.length; idx++) {
        const el = nodeList[idx];
        const d = svg.createDrawable(el);

        // Phase 1: Draw (from nothing to fully drawn)
        animate(d, {
          draw: ["0 0", "0 1"],
          easing: "easeOutQuad",
          duration: drawDuration,
          delay: actualDelay + idx * staggerDelay,
          loop: false,
        });

        // Phase 2: Undraw (from fully drawn to gone)
        const undrawStartTime =
          actualDelay + totalDrawTime + pauseDuration + idx * staggerDelay;
        setTimeout(() => {
          if (d) {
            animate(d, {
              draw: ["0 1", "1 1"],
              easing: "easeInQuad",
              duration: undrawDuration,
              loop: false,
            });
          }
        }, undrawStartTime);
      }

      // Set a single timeout to mark the animation as complete
      setTimeout(() => {
        animatePathsCompleted();
        // Ensure logo is in fully drawn state after animation completes
        setTimeout(() => {
          initializePaths();
          animatePathsCompleted(); // Ensure animation state is reset
        }, 100);
      }, totalAnimationDuration);
    } catch (e) {
      console.error("Failed to animate logo paths:", e);
      // Ensure the lock is released on error
      animatePathsCompleted();
    }
  }, [
    duration,
    delay,
    strokeColor,
    animatePathsStarted,
    animatePathsCompleted,
  ]);

  // Mount trigger
  useEffect(() => {
    if (trigger === "mount" || trigger === "both" || trigger === "mount-hover") {
      animatePaths();
    } else {
      // For hover/scroll, initialize paths to their final state
      initializePaths();
    }
  }, [animatePaths, initializePaths, trigger]);

  // Hover trigger
  useEffect(() => {
    if (trigger === "hover" || trigger === "both" || trigger === "mount-hover") {
      const handleHover = () => {
        // The check for animationRunning is now inside animatePaths
        // Use false to skip delay for hover animations
        animatePaths(false);
      };

      const node = containerRef.current;
      if (node) {
        node.addEventListener("mouseenter", handleHover);
        return () => node.removeEventListener("mouseenter", handleHover);
      }
    }
  }, [animatePaths, trigger]);

  // Scroll trigger (using Intersection Observer)
  useEffect(() => {
    if (trigger === "scroll" || trigger === "both") {
      const node = containerRef.current;
      if (node) {
        observerRef.current?.disconnect();
        observerRef.current = new IntersectionObserver(
          (entries) => {
            if (entries[0].isIntersecting) {
              // Use false to skip delay for scroll animations
              animatePaths(false);
            }
          },
          { threshold: 0.5 },
        );
        observerRef.current.observe(node);
        return () => observerRef.current?.disconnect();
      }
    }
  }, [animatePaths, trigger]);

  // Kill ongoing animation when tab loses focus.
  useEffect(() => {
    const killAnimation = () => {
      // Always attempt to kill animation on visibility change, even if not marked as running
      // This helps catch edge cases where the animation state is inconsistent

      // Stop all animations
      try {
        if (containerRef.current) {
          const svgEl = containerRef.current.querySelector("svg");
          if (svgEl) {
            const nodeList = svgEl.querySelectorAll("path, line, polyline");
            utils.remove(nodeList);
          }
        }

        animatePathsCompleted(); // Reset animation state
        lastAnimationStart.current = 0; // Clear the last animation start time

        // Force logo to final state
        setTimeout(() => {
          initializePaths(); // Ensure the logo is in its final state
          animatePathsCompleted(); // Double-ensure animation state is reset
        }, 0);
      } catch (e) {
        console.error("Failed to kill animation:", e);
      } finally {
        animatePathsCompleted(); // Ensure the animation lock is released
      }
    };


    const handleVisibilityChange = () => {
      if (document.visibilityState === "hidden") {
        // Always kill animation when tab loses focus, regardless of animation state
        killAnimation();
      } else if (document.visibilityState === "visible") {
        // Immediately reset when tab becomes visible again
        killAnimation(); // First kill any potentially running animations
        initializePaths(); // Then ensure paths are in their final state
      }
    };

    const handlePageShow = (event: any) => {
      // Need to reset if the user navigates back/forward to the page.
      // The check for event.persisted ensures this only runs on navigation events.
      // Note: This event listener is added as a catch-all, but visibilitychange should be the primary handler.
      if (event.persisted) {
        setTimeout(() => initializePaths(), 50);
      }
    };

    // Add event listeners for both proactive and reactive handling
    document.addEventListener("visibilitychange", handleVisibilityChange);
    window.addEventListener("pageshow", handlePageShow);

    return () => {
      document.removeEventListener("visibilitychange", handleVisibilityChange);
      window.removeEventListener("pageshow", handlePageShow);
    };
  }, [initializePaths, duration, delay]); // ensure that hooks that use 'killAnimation' are specified here.
  // Enhanced safety check mechanism
  useEffect(() => {
    const safetyCheck = () => {
      // Check if tab is visible
      if (document.visibilityState === "visible") {
        const now = Date.now();
        
        // Check for stuck animations - either running too long or in inconsistent state
        if (
          // Case 1: Animation has been running for too long
          (animationRunning.current &&
           lastAnimationStart.current > 0 &&
           (now - lastAnimationStart.current) > (duration + delay + 2000)) ||
          
          // Case 2: Animation state might be inconsistent (no recent animation but not fully drawn)
          (!animationRunning.current &&
           lastAnimationStart.current > 0 &&
           (now - lastAnimationStart.current) > 5000)
        ) {
          console.warn("Logo animation potentially stuck, resetting...");

          // Force cleanup and reset
          if (containerRef.current) {
            const svgEl = containerRef.current.querySelector("svg");
            if (svgEl) {
              const nodeList = svgEl.querySelectorAll("path, line, polyline");
              utils.remove(nodeList);
            }
          }

          // Reset all animation state
          animationRunning.current = false;
          lastAnimationStart.current = 0;
          
          // Force paths to final state
          setTimeout(() => {
            initializePaths();
          }, 0);
        }
      }
    };

    // Run safety check more frequently to catch issues faster
    const interval = setInterval(safetyCheck, 3000);

    return () => clearInterval(interval);
  }, [duration, delay, initializePaths]);

  if (!SVGComponent) return null;

  return (
    <div ref={containerRef} className={className} style={style}>
      <SVGComponent
        style={{
          display: "block",
          width: "100%",
          height: "100%",
          overflow: "visible",
        }}
      />
    </div>
  );
};

export default memo(AnimatedLogo);
